import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

// Database connection configuration
const connectionString = process.env.DATABASE_URL || 'postgresql://postgres.bnldxbomhzfbzdkploob:<EMAIL>:6543/postgres';

const pool = new Pool({
  connectionString: connectionString,
  ssl: { rejectUnauthorized: false }
});

// Test database connection
pool.on('connect', () => {
  console.log('Connected to PostgreSQL database');
});

pool.on('error', (err) => {
  console.error('Database connection error:', err);
});

// Test the connection immediately
async function testConnection() {
  try {
    console.log('Testing database connection...');
    console.log('Using connection string:', connectionString ? 'Available' : 'Not available');
    const client = await pool.connect();
    console.log('Database connection test successful');
    client.release();
  } catch (err) {
    console.error('Database connection test failed:', err.message);
  }
}

testConnection();

export default pool;
