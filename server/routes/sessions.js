import express from 'express';
import pool from '../db.js';

const router = express.Router();

// GET /api/sessions - Retrieve all milking sessions
router.get('/sessions', async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT * FROM milking_sessions ORDER BY created_at DESC'
    );
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching sessions:', error);
    res.status(500).json({ error: 'Failed to fetch sessions' });
  }
});

// POST /api/sessions - Save a new milking session
router.post('/sessions', async (req, res) => {
  try {
    const { start_time, end_time, duration, milk_quantity } = req.body;

    // Validation
    if (!start_time || !end_time || !duration || milk_quantity === undefined) {
      return res.status(400).json({ 
        error: 'Missing required fields: start_time, end_time, duration, milk_quantity' 
      });
    }

    if (duration <= 0) {
      return res.status(400).json({ error: 'Duration must be greater than 0' });
    }

    if (milk_quantity < 0) {
      return res.status(400).json({ error: 'Milk quantity cannot be negative' });
    }

    const result = await pool.query(
      `INSERT INTO milking_sessions (start_time, end_time, duration, milk_quantity) 
       VALUES ($1, $2, $3, $4) 
       RETURNING *`,
      [start_time, end_time, duration, milk_quantity]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating session:', error);
    res.status(500).json({ error: 'Failed to create session' });
  }
});

export default router;
