import { useState, useEffect } from 'react'
import './MilkingHistory.css'

function MilkingHistory({ onNavigate }) {
  const [sessions, setSessions] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    fetchSessions()
  }, [])

  const fetchSessions = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/sessions')
      if (response.ok) {
        const data = await response.json()
        setSessions(data)
      } else {
        console.error('Failed to fetch sessions:', response)
        setError('Failed to fetch sessions')
      }
    } catch (err) {
      setError('Failed to fetch sessions')
      console.error('Error fetching sessions:', err)
    } finally {
      setLoading(false)
    }
  }

  const formatDateTime = (dateString) => {
    const date = new Date(dateString)
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  }

  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  const getTotalStats = () => {
    const totalSessions = sessions.length
    const totalMilk = sessions.reduce((sum, session) => sum + parseFloat(session.milk_quantity), 0)
    const totalDuration = sessions.reduce((sum, session) => sum + session.duration, 0)
    const avgMilk = totalSessions > 0 ? totalMilk / totalSessions : 0

    return {
      totalSessions,
      totalMilk: totalMilk.toFixed(1),
      totalDuration: formatDuration(totalDuration),
      avgMilk: avgMilk.toFixed(1)
    }
  }

  const stats = getTotalStats()

  if (loading) {
    return (
      <div className="milking-history">
        <div className="container">
          <div className="loading">Loading sessions...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="milking-history">
        <div className="container">
          <div className="error">
            <p>{error}</p>
            <button onClick={fetchSessions}>Try Again</button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="milking-history">
      <div className="container">
        <header className="header">
          <button className="back-button" onClick={() => onNavigate('landing')}>
            ← Back
          </button>
          <h1>📊 Milking History</h1>
        </header>

        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-value">{stats.totalSessions}</div>
            <div className="stat-label">Total Sessions</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{stats.totalMilk}L</div>
            <div className="stat-label">Total Milk</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{stats.avgMilk}L</div>
            <div className="stat-label">Avg per Session</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{stats.totalDuration}</div>
            <div className="stat-label">Total Time</div>
          </div>
        </div>

        {sessions.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">🐄</div>
            <h3>No milking sessions yet</h3>
            <p>Start your first milking session to see the history here.</p>
            <button
              className="start-session-button"
              onClick={() => onNavigate('session')}
            >
              🎵 Start First Session
            </button>
          </div>
        ) : (
          <div className="sessions-table">
            <div className="table-header">
              <div className="header-cell">Date</div>
              <div className="header-cell">Start</div>
              <div className="header-cell">End</div>
              <div className="header-cell">Duration</div>
              <div className="header-cell">Milk (L)</div>
            </div>

            <div className="table-body">
              {sessions.map((session) => {
                const startDateTime = formatDateTime(session.start_time)
                const endDateTime = formatDateTime(session.end_time)

                return (
                  <div key={session.id} className="table-row">
                    <div className="table-cell">{startDateTime.date}</div>
                    <div className="table-cell">{startDateTime.time}</div>
                    <div className="table-cell">{endDateTime.time}</div>
                    <div className="table-cell">{formatDuration(session.duration)}</div>
                    <div className="table-cell">{parseFloat(session.milk_quantity).toFixed(1)}</div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        <div className="actions">
          <button
            className="new-session-button"
            onClick={() => onNavigate('session')}
          >
            🎵 New Session
          </button>
          <button
            className="refresh-button"
            onClick={fetchSessions}
          >
            🔄 Refresh
          </button>
        </div>
      </div>
    </div>
  )
}

export default MilkingHistory
