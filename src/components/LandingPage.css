.landing-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.header {
  margin-bottom: 40px;
}

.title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 700;
}

.subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.start-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 18px 30px;
  font-size: 1.3rem;
  font-weight: 600;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.start-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.history-button {
  background: #ffffff;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-button:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.info-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #667eea;
}

.info-card h3 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.info-card p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }
  
  .start-button {
    font-size: 1.2rem;
    padding: 16px 25px;
  }
  
  .history-button {
    font-size: 1rem;
    padding: 14px 25px;
  }
}
