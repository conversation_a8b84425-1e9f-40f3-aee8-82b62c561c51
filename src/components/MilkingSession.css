.milking-session {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: white;
}

.container {
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40px;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.header h1 {
  font-size: 1.8rem;
  margin: 0;
  flex-grow: 1;
  text-align: center;
}

.timer-display {
  margin-bottom: 50px;
}

.timer {
  font-size: 4rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 20px;
  margin-bottom: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.timer-label {
  font-size: 1.2rem;
  opacity: 0.9;
}

.controls {
  margin-bottom: 40px;
}

.start-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid white;
  padding: 18px 40px;
  font-size: 1.3rem;
  font-weight: 600;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.start-button:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
}

.session-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.pause-button, .stop-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 15px 25px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.pause-button:hover, .stop-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.pause-button.resume {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

.music-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.music-info p {
  margin: 0;
  font-size: 1rem;
  opacity: 0.9;
}

/* Milk input styles */
.session-summary {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.milk-input-section {
  margin-bottom: 30px;
}

.milk-input-section label {
  display: block;
  margin-bottom: 10px;
  font-size: 1.1rem;
  font-weight: 600;
}

.milk-input-section input {
  width: 100%;
  padding: 15px;
  font-size: 1.1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
  box-sizing: border-box;
}

.milk-input-section input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.milk-input-section input:focus {
  outline: none;
  border-color: white;
  background: rgba(255, 255, 255, 0.2);
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.save-button {
  background: rgba(76, 175, 80, 0.3);
  color: white;
  border: 2px solid rgba(76, 175, 80, 0.5);
  padding: 15px 25px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.save-button:hover {
  background: rgba(76, 175, 80, 0.5);
  transform: translateY(-1px);
}

.cancel-button {
  background: rgba(244, 67, 54, 0.3);
  color: white;
  border: 2px solid rgba(244, 67, 54, 0.5);
  padding: 15px 25px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.cancel-button:hover {
  background: rgba(244, 67, 54, 0.5);
  transform: translateY(-1px);
}

@media (max-width: 480px) {
  .timer {
    font-size: 3rem;
    padding: 20px;
  }
  
  .session-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .pause-button, .stop-button, .save-button, .cancel-button {
    width: 100%;
    max-width: 200px;
  }
}
