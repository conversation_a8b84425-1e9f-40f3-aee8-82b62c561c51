import { useState, useEffect, useRef } from 'react'
import './MilkingSession.css'

function MilkingSession({ onNavigate }) {
  const [isRunning, setIsRunning] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [seconds, setSeconds] = useState(0)
  const [startTime, setStartTime] = useState(null)
  const [showMilkInput, setShowMilkInput] = useState(false)
  const [milkQuantity, setMilkQuantity] = useState('')
  const audioRef = useRef(null)

  useEffect(() => {
    let interval = null
    if (isRunning && !isPaused) {
      interval = setInterval(() => {
        setSeconds(seconds => seconds + 1)
      }, 1000)
    } else if (!isRunning) {
      clearInterval(interval)
    }
    return () => clearInterval(interval)
  }, [isRunning, isPaused])

  const formatTime = (totalSeconds) => {
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const secs = totalSeconds % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const handleStart = () => {
    setIsRunning(true)
    setIsPaused(false)
    setStartTime(new Date().toISOString())
    
    // Start music
    if (audioRef.current) {
      audioRef.current.play().catch(e => console.log('Audio play failed:', e))
    }
  }

  const handlePause = () => {
    setIsPaused(!isPaused)
    
    // Pause/resume music
    if (audioRef.current) {
      if (isPaused) {
        audioRef.current.play().catch(e => console.log('Audio play failed:', e))
      } else {
        audioRef.current.pause()
      }
    }
  }

  const handleStop = () => {
    setIsRunning(false)
    setIsPaused(false)
    setShowMilkInput(true)
    
    // Stop music
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
    }
  }

  const handleSaveSession = async () => {
    if (!milkQuantity || parseFloat(milkQuantity) < 0) {
      alert('Please enter a valid milk quantity')
      return
    }

    const endTime = new Date().toISOString()
    const sessionData = {
      start_time: startTime,
      end_time: endTime,
      duration: seconds,
      milk_quantity: parseFloat(milkQuantity)
    }

    try {
      const response = await fetch('/api/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sessionData),
      })

      if (response.ok) {
        alert('Session saved successfully!')
        onNavigate('landing')
      } else {
        alert('Failed to save session')
      }
    } catch (error) {
      console.error('Error saving session:', error)
      alert('Failed to save session')
    }
  }

  const handleCancel = () => {
    setSeconds(0)
    setStartTime(null)
    setShowMilkInput(false)
    setMilkQuantity('')
    onNavigate('landing')
  }

  if (showMilkInput) {
    return (
      <div className="milking-session">
        <div className="container">
          <h2>Session Complete! 🎉</h2>
          <div className="session-summary">
            <p><strong>Duration:</strong> {formatTime(seconds)}</p>
          </div>
          
          <div className="milk-input-section">
            <label htmlFor="milkQuantity">Milk Collected (liters):</label>
            <input
              type="number"
              id="milkQuantity"
              value={milkQuantity}
              onChange={(e) => setMilkQuantity(e.target.value)}
              placeholder="Enter quantity"
              step="0.1"
              min="0"
            />
          </div>

          <div className="action-buttons">
            <button className="save-button" onClick={handleSaveSession}>
              💾 Save Session
            </button>
            <button className="cancel-button" onClick={handleCancel}>
              ❌ Cancel
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="milking-session">
      <div className="container">
        <header className="header">
          <button className="back-button" onClick={() => onNavigate('landing')}>
            ← Back
          </button>
          <h1>🐄 Milking Session</h1>
        </header>

        <div className="timer-display">
          <div className="timer">{formatTime(seconds)}</div>
          <div className="timer-label">
            {!isRunning ? 'Ready to start' : isPaused ? 'Paused' : 'In progress'}
          </div>
        </div>

        <div className="controls">
          {!isRunning ? (
            <button className="start-button" onClick={handleStart}>
              🎵 Start Session
            </button>
          ) : (
            <div className="session-controls">
              <button 
                className={`pause-button ${isPaused ? 'resume' : ''}`}
                onClick={handlePause}
              >
                {isPaused ? '▶️ Resume' : '⏸️ Pause'}
              </button>
              <button className="stop-button" onClick={handleStop}>
                ⏹️ Stop
              </button>
            </div>
          )}
        </div>

        <div className="music-info">
          <p>🎶 Calming music is playing to help reduce stress</p>
        </div>

        {/* Hidden audio element for music */}
        <audio
          ref={audioRef}
          loop
          preload="auto"
        >
          <source src="/calm-music.mp3" type="audio/mpeg" />
          Your browser does not support the audio element.
        </audio>
      </div>
    </div>
  )
}

export default MilkingSession
