import './LandingPage.css'

function LandingPage({ onNavigate }) {
  return (
    <div className="landing-page">
      <div className="container">
        <header className="header">
          <h1 className="title">🐄 Milking Tracker</h1>
          <p className="subtitle">Track your milking sessions with soothing music</p>
        </header>

        <main className="main-content">
          <div className="action-buttons">
            <button 
              className="start-button"
              onClick={() => onNavigate('session')}
            >
              🎵 Start Milking
            </button>
            
            <button 
              className="history-button"
              onClick={() => onNavigate('history')}
            >
              📊 Milking History
            </button>
          </div>

          <div className="info-card">
            <h3>🎶 Music helps increase milk yield by up to 3%</h3>
            <p>Studies show that playing calming music creates a stress-free environment for cattle, leading to better milk production.</p>
          </div>
        </main>
      </div>
    </div>
  )
}

export default LandingPage
