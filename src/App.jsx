import { useState } from 'react'
import './App.css'
import LandingPage from './components/LandingPage'
import MilkingSession from './components/MilkingSession'
import MilkingHistory from './components/MilkingHistory'

function App() {
  const [currentPage, setCurrentPage] = useState('landing')

  const renderPage = () => {
    switch (currentPage) {
      case 'landing':
        return <LandingPage onNavigate={setCurrentPage} />
      case 'session':
        return <MilkingSession onNavigate={setCurrentPage} />
      case 'history':
        return <MilkingHistory onNavigate={setCurrentPage} />
      default:
        return <LandingPage onNavigate={setCurrentPage} />
    }
  }

  return (
    <div className="app">
      {renderPage()}
    </div>
  )
}

export default App
