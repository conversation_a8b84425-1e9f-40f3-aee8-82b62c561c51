# 🐄 Milking Tracker with Music

A mobile-first web application that helps dairy farmers track milking sessions while playing relaxing music for cattle to reduce stress and increase milk yield.

## Features

- **Landing Page**: Simple interface with "Start Milking" and "Milking History" options
- **Milking Session**: Timer with music playback, pause/resume/stop controls
- **Session Recording**: Save milk quantity and session details
- **History View**: Display past sessions with statistics
- **Mobile-First Design**: Responsive design optimized for mobile devices
- **Music Integration**: Calming music plays during sessions to reduce cattle stress

## Tech Stack

- **Frontend**: React (JavaScript), Vite
- **Backend**: Node.js, Express
- **Database**: PostgreSQL (Supabase)
- **Styling**: CSS with mobile-first approach
- **Deployment**: Vercel-ready configuration

## Setup Instructions

### Prerequisites
- Node.js (v18 or higher)
- Supabase account with PostgreSQL database

### Installation

1. Install dependencies:
```bash
npm install
```

2. Configure environment variables:
   - Update `.env` file and replace `[YOUR-PASSWORD]` with your actual Supabase password

3. Database Setup:
   The `milking_sessions` table should already be created in your Supabase database.

### Development

1. Start the backend server:
```bash
npm run server
```

2. In a new terminal, start the frontend development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

### Production Build

```bash
npm run build
npm run start
```

## API Endpoints

- `GET /api/sessions` - Retrieve all milking sessions
- `POST /api/sessions` - Create a new milking session
- `GET /api/health` - Health check endpoint

## Deployment

The application is configured for Vercel deployment:

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard:
   - `DATABASE_URL`: Your Supabase connection string
   - `NODE_ENV`: production
3. Deploy

## Music Setup

To add actual music:
1. Place your MP3 file in the `public` directory as `calm-music.mp3`
2. The application will automatically play it during milking sessions

## Project Structure

```
├── public/                 # Static assets
├── server/                 # Backend API
│   ├── index.js           # Express server
│   ├── db.js              # Database connection
│   └── routes/            # API routes
├── src/                   # Frontend source
│   ├── components/        # React components
│   ├── App.jsx           # Main app component
│   └── main.jsx          # Entry point
├── .env                  # Environment variables
├── vercel.json           # Vercel deployment config
└── package.json          # Dependencies and scripts
```
